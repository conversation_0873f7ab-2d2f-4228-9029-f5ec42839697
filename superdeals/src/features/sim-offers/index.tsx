'use client';

import { useState } from 'react';
import { HomeScreen } from './components/screens/HomeScreen';
import { OffersScreen } from './components/screens/OffersScreen';
import { OfferDetailScreen } from './components/screens/OfferDetailScreen';
import { SearchScreen } from './components/screens/SearchScreen';
import { FavoritesScreen } from './components/screens/FavoritesScreen';
import { ProfileScreen } from './components/screens/ProfileScreen';
import { SAMPLE_OFFERS } from './data/offers';
import type { Screen, Offer } from './types';

export default function SimOffersApp() {
  const [currentScreen, setCurrentScreen] = useState<Screen>('home');
  const [selectedOffer, setSelectedOffer] = useState<Offer>(SAMPLE_OFFERS[0]); // Default to first offer
  const [activeTab, setActiveTab] = useState('home');

  const handleNavigation = (screen: Screen) => {
    setCurrentScreen(screen);

    // Update active tab based on screen
    switch (screen) {
      case 'home':
        setActiveTab('home');
        break;
      case 'offers':
      case 'offer-detail':
        setActiveTab('offers');
        break;
      case 'search':
        setActiveTab('search');
        break;
      case 'favorites':
        setActiveTab('favorites');
        break;
      case 'profile':
        setActiveTab('profile');
        break;
      default:
        break;
    }
  };

  const handleOfferSelection = (offer: Offer) => {
    setSelectedOffer(offer);
  };

  const renderScreen = () => {
    const commonProps = {
      onNavigate: handleNavigation,
      activeTab,
    };

    switch (currentScreen) {
      case 'home':
        return (
          <HomeScreen
            {...commonProps}
            onOfferSelect={handleOfferSelection}
          />
        );

      case 'offers':
        return (
          <OffersScreen
            {...commonProps}
            onOfferSelect={handleOfferSelection}
          />
        );

      case 'offer-detail':
        return (
          <OfferDetailScreen
            {...commonProps}
            offer={selectedOffer}
          />
        );

      case 'search':
        return (
          <SearchScreen
            {...commonProps}
            onOfferSelect={handleOfferSelection}
          />
        );

      case 'favorites':
        return (
          <FavoritesScreen
            {...commonProps}
            onOfferSelect={handleOfferSelection}
          />
        );

      case 'profile':
        return (
          <ProfileScreen
            {...commonProps}
          />
        );

      default:
        return (
          <HomeScreen
            {...commonProps}
            onOfferSelect={handleOfferSelection}
          />
        );
    }
  };

  return (
    <div className="w-full max-w-sm mx-auto bg-white min-h-screen">
      {renderScreen()}
    </div>
  );
}