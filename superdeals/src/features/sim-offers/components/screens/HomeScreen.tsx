'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, Search, User, Grid3X3, Home, Star } from 'lucide-react';
import { SAMPLE_OFFERS } from '../../data/offers';
import type { Screen, Offer, ScreenProps } from '../../types';

interface HomeScreenProps extends ScreenProps {}

export function HomeScreen({ onNavigate, onOfferSelect, activeTab }: HomeScreenProps) {
  const [favorites, setFavorites] = useState<string[]>([]);

  const toggleFavorite = (offerId: string) => {
    setFavorites(prev => 
      prev.includes(offerId) 
        ? prev.filter(id => id !== offerId)
        : [...prev, offerId]
    );
  };

  const handleOfferClick = (offer: Offer) => {
    onOfferSelect(offer);
    onNavigate('offer-detail');
  };

  const featuredOffers = SAMPLE_OFFERS.slice(0, 3);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-xl font-bold">রিয়াদের সিম অফার</h1>
            <p className="text-green-100 text-sm">Best SIM offers at wholesale prices</p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-green-600"
            onClick={() => onNavigate('search')}
          >
            <Search className="h-5 w-5" />
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-3 text-center">
          <div className="bg-green-600/50 rounded-lg p-3">
            <div className="text-lg font-bold">50+</div>
            <div className="text-xs text-green-100">Active Offers</div>
          </div>
          <div className="bg-green-600/50 rounded-lg p-3">
            <div className="text-lg font-bold">24/7</div>
            <div className="text-xs text-green-100">Support</div>
          </div>
          <div className="bg-green-600/50 rounded-lg p-3">
            <div className="text-lg font-bold">৳50+</div>
            <div className="text-xs text-green-100">Avg Savings</div>
          </div>
        </div>
      </div>

      {/* Featured Offers */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-800">Featured Offers</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onNavigate('offers')}
          >
            View All
          </Button>
        </div>

        <div className="space-y-3">
          {featuredOffers.map((offer) => (
            <Card 
              key={offer.id} 
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleOfferClick(offer)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary" className="text-xs">
                        {offer.provider}
                      </Badge>
                      {offer.is_featured && (
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      )}
                    </div>
                    <h3 className="font-semibold text-gray-800 mb-1">{offer.title_bn}</h3>
                    <p className="text-sm text-gray-600 mb-2">{offer.title_en}</p>
                    <div className="flex items-center gap-4 text-sm">
                      <span className="text-green-600 font-semibold">{offer.data_amount}{offer.data_unit}</span>
                      <span className="text-gray-500">{offer.validity_days} days</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">৳{offer.my_price}</div>
                    <div className="text-xs text-gray-500 line-through">৳{offer.company_price}</div>
                    <div className="text-xs text-red-500">Save ৳{offer.savings}</div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="mt-2"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleFavorite(offer.id);
                      }}
                    >
                      <Heart 
                        className={`h-4 w-4 ${
                          favorites.includes(offer.id) 
                            ? 'text-red-500 fill-current' 
                            : 'text-gray-400'
                        }`} 
                      />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 gap-3">
          <Button
            variant="outline"
            className="h-20 flex-col gap-2"
            onClick={() => onNavigate('offers')}
          >
            <Grid3X3 className="h-6 w-6" />
            <span className="text-sm">All Offers</span>
          </Button>
          <Button
            variant="outline"
            className="h-20 flex-col gap-2"
            onClick={() => onNavigate('favorites')}
          >
            <Heart className="h-6 w-6" />
            <span className="text-sm">Favorites</span>
          </Button>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="max-w-sm mx-auto">
          <div className="grid grid-cols-5 py-2">
            <Button
              variant="ghost"
              className={`flex-col gap-1 h-16 ${activeTab === 'home' ? 'text-green-600' : 'text-gray-400'}`}
              onClick={() => onNavigate('home')}
            >
              <Home className="h-5 w-5" />
              <span className="text-xs">Home</span>
            </Button>
            <Button
              variant="ghost"
              className={`flex-col gap-1 h-16 ${activeTab === 'offers' ? 'text-green-600' : 'text-gray-400'}`}
              onClick={() => onNavigate('offers')}
            >
              <Grid3X3 className="h-5 w-5" />
              <span className="text-xs">Offers</span>
            </Button>
            <Button
              variant="ghost"
              className={`flex-col gap-1 h-16 ${activeTab === 'search' ? 'text-green-600' : 'text-gray-400'}`}
              onClick={() => onNavigate('search')}
            >
              <Search className="h-5 w-5" />
              <span className="text-xs">Search</span>
            </Button>
            <Button
              variant="ghost"
              className={`flex-col gap-1 h-16 ${activeTab === 'favorites' ? 'text-green-600' : 'text-gray-400'}`}
              onClick={() => onNavigate('favorites')}
            >
              <Heart className="h-5 w-5" />
              <span className="text-xs">Favorites</span>
            </Button>
            <Button
              variant="ghost"
              className={`flex-col gap-1 h-16 ${activeTab === 'profile' ? 'text-green-600' : 'text-gray-400'}`}
              onClick={() => onNavigate('profile')}
            >
              <User className="h-5 w-5" />
              <span className="text-xs">Profile</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-20"></div>
    </div>
  );
}
